package com.datalink.common.utils;

import com.datalink.common.DataConstants;
import com.datalink.system.service.ISysConfigService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * SAP接口调用工具类
 * 
 * <AUTHOR>
 * @date 2025-05-24
 */
@Component
public class SapApiClient {

    private static final Logger logger = LoggerFactory.getLogger(SapApiClient.class);

    @Autowired
    private ISysConfigService configService;

    /**
     * 调用SAP接口（使用配置中的默认URL）
     *
     * @param itemList 数据列表
     * @return SAP接口返回结果
     */
    public HashMap<String, Object> callSapApi(List<?> itemList) {
        String sapUrl = configService.selectConfigByKey(DataConstants.SAP_ASN_URL);
        return callSapApi(sapUrl, itemList);
    }

    /**
     * 调用SAP接口（指定URL）
     *
     * @param sapUrl SAP接口URL
     * @param itemList 数据列表
     * @return SAP接口返回结果
     */
    public HashMap<String, Object> callSapApi(String sapUrl, List<?> itemList) {
        try {
            logger.info("开始调用SAP接口，URL: {}, 数据条数: {}", sapUrl, itemList != null ? itemList.size() : 0);

            // 构建请求体
            Map<String, Object> body = new HashMap<>();
            body.put("Item", itemList);

            // 获取SAP认证信息
            String sapUser = configService.selectConfigByKey(DataConstants.SAP_USER);
            String sapPass = configService.selectConfigByKey(DataConstants.SAP_PASS);

            // 创建RestTemplate
            RestTemplate restTemplate = new RestTemplate();
            
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 设置Basic认证
            String auth = sapUser + ":" + sapPass;
            String encodedAuth = Base64.getEncoder().encodeToString(auth.getBytes());
            headers.set("Authorization", "Basic " + encodedAuth);

            // 创建请求实体
            HttpEntity<Object> entity = new HttpEntity<>(body, headers);

            // 调用SAP接口
            HashMap<String, Object> result = restTemplate.postForObject(sapUrl, entity, HashMap.class);
            
            logger.info("SAP接口调用成功，返回结果: {}", result);
            return result;

        } catch (Exception e) {
            logger.error("调用SAP接口失败，URL: {}, 错误信息: {}", sapUrl, e.getMessage(), e);
            throw new RuntimeException("调用SAP接口失败: " + e.getMessage(), e);
        }
    }

    /**
     * 调用SAP接口（自定义请求体）
     *
     * @param sapUrl SAP接口URL
     * @param requestBody 自定义请求体
     * @return SAP接口返回结果
     */
    public HashMap<String, Object> callSapApi(String sapUrl, Map<String, Object> requestBody) {
        try {
            logger.info("开始调用SAP接口，URL: {}, 请求体: {}", sapUrl, requestBody);

            // 获取SAP认证信息
            String sapUser = configService.selectConfigByKey(DataConstants.SAP_USER);
            String sapPass = configService.selectConfigByKey(DataConstants.SAP_PASS);

            // 创建RestTemplate
            RestTemplate restTemplate = new RestTemplate();
            
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 设置Basic认证
            String auth = sapUser + ":" + sapPass;
            String encodedAuth = Base64.getEncoder().encodeToString(auth.getBytes());
            headers.set("Authorization", "Basic " + encodedAuth);

            // 创建请求实体
            HttpEntity<Object> entity = new HttpEntity<>(requestBody, headers);

            // 调用SAP接口
            HashMap<String, Object> result = restTemplate.postForObject(sapUrl, entity, HashMap.class);
            
            logger.info("SAP接口调用成功，返回结果: {}", result);
            return result;

        } catch (Exception e) {
            logger.error("调用SAP接口失败，URL: {}, 错误信息: {}", sapUrl, e.getMessage(), e);
            throw new RuntimeException("调用SAP接口失败: " + e.getMessage(), e);
        }
    }

    /**
     * 调用SAP接口（完全自定义）
     *
     * @param sapUrl SAP接口URL
     * @param sapUser SAP用户名
     * @param sapPass SAP密码
     * @param requestBody 请求体
     * @return SAP接口返回结果
     */
    public HashMap<String, Object> callSapApi(String sapUrl, String sapUser, String sapPass, Map<String, Object> requestBody) {
        try {
            logger.info("开始调用SAP接口，URL: {}, 用户: {}", sapUrl, sapUser);

            // 创建RestTemplate
            RestTemplate restTemplate = new RestTemplate();
            
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 设置Basic认证
            String auth = sapUser + ":" + sapPass;
            String encodedAuth = Base64.getEncoder().encodeToString(auth.getBytes());
            headers.set("Authorization", "Basic " + encodedAuth);

            // 创建请求实体
            HttpEntity<Object> entity = new HttpEntity<>(requestBody, headers);

            // 调用SAP接口
            HashMap<String, Object> result = restTemplate.postForObject(sapUrl, entity, HashMap.class);
            
            logger.info("SAP接口调用成功，返回结果: {}", result);
            return result;

        } catch (Exception e) {
            logger.error("调用SAP接口失败，URL: {}, 错误信息: {}", sapUrl, e.getMessage(), e);
            throw new RuntimeException("调用SAP接口失败: " + e.getMessage(), e);
        }
    }
}
