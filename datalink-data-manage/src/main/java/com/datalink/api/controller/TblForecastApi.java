package com.datalink.api.controller;

import com.datalink.api.common.ApiResult;
import com.datalink.api.common.CommonRequestEntity;
import com.datalink.api.domain.dto.TblForecastBatchRequestDTO;
import com.datalink.api.domain.dto.TblForecastItemDTO;
import com.datalink.common.DataConstants;
import com.datalink.common.annotation.JacksonFilter;
import com.datalink.common.annotation.Log;
import com.datalink.common.enums.BusinessType;
import com.datalink.common.utils.SecurityUtils;
import com.datalink.common.utils.StringUtils;
import com.datalink.datamanage.domain.TblForecast;
import com.datalink.datamanage.domain.TblForecastItem;
import com.datalink.datamanage.service.ITblForecastService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.compress.utils.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

import static com.datalink.api.common.SapObjectConverter.convertToTblForecast;

@Api("预测接口")
@Validated
@RestController
@RequestMapping("/api/forecast")
public class TblForecastApi {

    private static final Logger logger = LoggerFactory.getLogger(TblForecastApi.class);

    @Autowired
    private ITblForecastService forecastService;

    @ApiOperation("查询预测")
    @ApiImplicitParam(name = "commonRequestEntity", value = "查询信息", dataType = "CommonRequestEntity")
    @JacksonFilter(exclude={"forecastid","searchvalue","createby","updateby","updatetime","remark","params","createtime","itemid","direction","kafkastatus"}, value={TblForecast.class, TblForecastItem.class}, type= JacksonFilter.JscksonFilterType.RESPONSE)
    //@JacksonFilter(exclude={"itemid","orderid","createTime","searchValue","createBy","updateBy","updateTime","remark","param"}, value=TblForecastItem.class, type= JacksonFilter.JscksonFilterType.RESPONSE)
    @PostMapping("/query")
    @Log(title = "查询预测", businessType = BusinessType.EXPORT)
    public ApiResult query(@Valid @RequestBody CommonRequestEntity requestEntity) {
        ApiResult ajax = requestEntity.checkAndInit();
        List<TblForecast> forecastList = Lists.newArrayList();
        if(ajax.isSuccess()){
            TblForecast searchParam = new TblForecast();
            searchParam.setDirection(DataConstants.DIRECTION_IN);
            searchParam.setParams(requestEntity.getParams());
            forecastList = forecastService.selectTblForecastFullList(searchParam);
        }
        if(forecastList.isEmpty()){
            Long lastId = forecastService.selectLastId();
            ajax.put("cursor", null == lastId ? 0 : lastId);
            ajax.put("time", new Date());
        }else{
            ajax.put("cursor", ""+forecastList.get(forecastList.size()-1).getForecastId());
            ajax.put("time", forecastList.get(forecastList.size()-1).getCreateTime());
        }

        ajax.put("items", forecastList);
        return ajax;
    }

    @ApiOperation("发送")
    @ApiImplicitParam(name = "forecast", value = "预测信息", dataType = "TblForecast")
    @PostMapping("/send")
    @Transactional(rollbackFor = Exception.class)
    @Log(title = "发送预测", businessType = BusinessType.IMPORT)
    public ApiResult send(@Valid @RequestBody TblForecast tblForecast) {
        tblForecast.setDirection(DataConstants.DIRECTION_OUT);
        tblForecast.setKafkaStatus(DataConstants.KAFKA_STATUS_TO_SEND);
        tblForecast.setCreateBy(SecurityUtils.getUsername());
        forecastService.insertTblForecast(tblForecast);
        return ApiResult.success();
    }

    @ApiOperation("批量发送")
    @ApiImplicitParam(name = "forecast", value = "预测信息", dataType = "TblForecast", allowMultiple = true)
    @PostMapping("/batchSend")
    @Transactional(rollbackFor = Exception.class)
    @Log(title = "批量发送预测", businessType = BusinessType.IMPORT)
    public ApiResult batchSend(@Valid @RequestBody List<TblForecast> forecasts) {
        for (TblForecast forecast : forecasts){
            forecast.setDirection(DataConstants.DIRECTION_OUT);
            forecast.setKafkaStatus(DataConstants.KAFKA_STATUS_TO_SEND);
            forecast.setCreateBy(SecurityUtils.getUsername());
            forecastService.insertTblForecast(forecast);
        }

        return ApiResult.success();
    }


    @ApiOperation("批量接收预测")
    @ApiImplicitParam(name = "requestDTO", value = "预测信息", dataType = "TblForecastBatchRequestDTO")
    @PostMapping("/batchReceive")
    @Transactional(rollbackFor = Exception.class)
    @Log(title = "批量接收预测", businessType = BusinessType.IMPORT)
    public ApiResult batchReceive(@Valid @RequestBody TblForecastBatchRequestDTO requestDTO) {
        try {
            // 按照预测编号分组
            Map<String, List<TblForecastItemDTO>> groupedData = requestDTO.getData().stream()
                    .collect(Collectors.groupingBy(TblForecastItemDTO::getForecastCode));

            // 处理每个分组
            for (Map.Entry<String, List<TblForecastItemDTO>> entry : groupedData.entrySet()) {
                String forecastCode = entry.getKey();
                List<TblForecastItemDTO> items = entry.getValue();

                TblForecast forecast = convertToTblForecast(items);
                if (forecast != null) {
                    // 版本管理：查询当前预测编号的最大版本号
                    String maxVersion = forecastService.selectMaxVersionByForecastCode(forecastCode);
                    String newVersion;
                    if (StringUtils.isEmpty(maxVersion)) {
                        // 如果没有找到版本，说明是第一次，版本设为"1"
                        newVersion = "1";
                        logger.info("预测编号: {} 首次创建，版本号: {}", forecastCode, newVersion);
                    } else {
                        // 如果找到了版本，将版本号加1
                        try {
                            int currentVersion = Integer.parseInt(maxVersion);
                            newVersion = String.valueOf(currentVersion + 1);
                            logger.info("预测编号: {} 已存在，当前最大版本: {}，新版本: {}", forecastCode, maxVersion, newVersion);
                        } catch (NumberFormatException e) {
                            // 如果版本号不是数字，默认设为"1"
                            newVersion = "1";
                            logger.warn("预测编号: {} 的版本号格式异常: {}，重置为版本: {}", forecastCode, maxVersion, newVersion);
                        }
                    }

                    forecast.setVersion(newVersion);
                    forecast.setDirection(DataConstants.DIRECTION_OUT);
                    forecast.setKafkaStatus(DataConstants.KAFKA_STATUS_TO_SEND);
                    forecast.setStatus(DataConstants.FORECAST_STATUS_NEW);
                    forecast.setCreateBy(SecurityUtils.getUsername());
                    forecastService.insertTblForecast(forecast);
                    logger.info("成功保存预测编号: {}，版本: {}", forecastCode, newVersion);
                } else {
                    logger.warn("预测编号: {} 转换失败，跳过处理", forecastCode);
                }
            }

            return ApiResult.success();
        } catch (Exception e) {
            logger.error("批量处理预测失败: {}", e.getMessage(), e);
            throw e; // 由于方法已标记为@Transactional，异常会触发回滚
        }
    }
}
