package com.datalink.datamanage.mapper;

import com.datalink.common.annotation.DataScope;
import com.datalink.datamanage.domain.TblForecast;
import com.datalink.datamanage.domain.TblForecastItem;

import java.util.List;

/**
 * 预测Mapper接口
 *
 * <AUTHOR>
 * @date 2021-06-23
 */
public interface TblForecastMapper
{
    /**
     * 查询预测
     *
     * @param forecastId 预测ID
     * @return 预测
     */
    public TblForecast selectTblForecastById(Long forecastId);

    /**
     * 查询预测列表
     *
     * @param tblForecast 预测
     * @return 预测集合
     */
    public List<TblForecast> selectTblForecastList(TblForecast tblForecast);

    /**
     * 新增预测
     *
     * @param tblForecast 预测
     * @return 结果
     */
    public int insertTblForecast(TblForecast tblForecast);

    /**
     * 修改预测
     *
     * @param tblForecast 预测
     * @return 结果
     */
    public int updateTblForecast(TblForecast tblForecast);

    /**
     * 删除预测
     *
     * @param forecastId 预测ID
     * @return 结果
     */
    public int deleteTblForecastById(Long forecastId);

    /**
     * 批量删除预测
     *
     * @param forecastIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteTblForecastByIds(Long[] forecastIds);

    /**
     * 批量删除预测行项目
     *
     * @param forecastIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteTblForecastItemByForecastIds(Long[] forecastIds);

    /**
     * 批量新增预测行项目
     *
     * @param tblForecastItemList 预测行项目列表
     * @return 结果
     */
    public int batchTblForecastItem(List<TblForecastItem> tblForecastItemList);


    /**
     * 通过预测ID删除预测行项目信息
     *
     * @param forecastId 预测ID
     * @return 结果
     */
    public int deleteTblForecastItemByForecastId(Long forecastId);

    /**
     * 查询预测列表（包括预测行项目）--接口专用
     *
     * @param tblForecast 预测
     * @return 预测集合
     */
    public List<TblForecast> selectTblForecastFullList(TblForecast tblForecast);

    /**
     * 查询预测列表（包括预测行项目
     *
     * @param tblForecast 预测
     * @return 预测集合
     */
    public List<TblForecast> selectTblForecastWithItemList(TblForecast tblForecast);

    /**
     * 查询最大ID
     *
     * @return 最大ID
     */
    public Long selectLastId();

    /**
     * 查询预测行项目列表
     *
     * @param forecastItem 预测
     * @return 预测行项目集合
     */
    public List<TblForecastItem> selectTblForecastItemList(TblForecastItem forecastItem);

    /**
     * 查询预测(不抱行行项目)
     *
     * @param forecast 预测
     * @return 预测
     */
    @DataScope(supplierAlias = "a")
    public TblForecast selectTblForecastOnlyById(TblForecast forecast);

    /**
     * 查询指定预测编号的最大版本号
     *
     * @param forecastCode 预测编号
     * @return 最大版本号
     */
    public String selectMaxVersionByForecastCode(String forecastCode);

    /**
     * 查询每个预测编号的最新版本预测列表
     *
     * @param tblForecast 预测
     * @return 预测集合（每个预测编号只返回最新版本）
     */
    public List<TblForecast> selectTblForecastLatestVersionList(TblForecast tblForecast);

    /**
     * 查询每个预测编号的最新版本预测列表（包括预测行项目）
     *
     * @param tblForecast 预测
     * @return 预测集合（每个预测编号只返回最新版本，包含行项目）
     */
    public List<TblForecast> selectTblForecastLatestVersionWithItemList(TblForecast tblForecast);
}
