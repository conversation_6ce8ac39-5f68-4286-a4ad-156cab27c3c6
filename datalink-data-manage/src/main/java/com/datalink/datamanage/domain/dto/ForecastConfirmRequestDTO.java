package com.datalink.datamanage.domain.dto;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 预测确认请求DTO
 * 
 * <AUTHOR>
 * @date 2023-12-01
 */
public class ForecastConfirmRequestDTO {

    /** 预测ID列表 */
    @NotNull(message = "预测ID列表不能为空")
    @NotEmpty(message = "预测ID列表不能为空")
    private Long[] forecastIds;

    /** SAP接口URL */
    @NotNull(message = "SAP接口URL不能为空")
    @NotEmpty(message = "SAP接口URL不能为空")
    private String sapUrl;

    public Long[] getForecastIds() {
        return forecastIds;
    }

    public void setForecastIds(Long[] forecastIds) {
        this.forecastIds = forecastIds;
    }

    public String getSapUrl() {
        return sapUrl;
    }

    public void setSapUrl(String sapUrl) {
        this.sapUrl = sapUrl;
    }

    @Override
    public String toString() {
        return "ForecastConfirmRequestDTO{" +
                "forecastIds=" + java.util.Arrays.toString(forecastIds) +
                ", sapUrl='" + sapUrl + '\'' +
                '}';
    }
}
