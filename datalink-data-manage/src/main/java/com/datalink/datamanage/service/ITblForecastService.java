package com.datalink.datamanage.service;

import com.datalink.datamanage.domain.TblForecast;
import com.datalink.datamanage.domain.TblForecastItem;

import java.util.List;

/**
 * 预测Service接口
 *
 * <AUTHOR>
 * @date 2021-06-23
 */
public interface ITblForecastService
{
    /**
     * 查询预测
     *
     * @param forecastId 预测ID
     * @return 预测
     */
    public TblForecast selectTblForecastById(Long forecastId);

    /**
     * 查询预测列表
     *
     * @param tblForecast 预测
     * @return 预测集合
     */
    public List<TblForecast> selectTblForecastList(TblForecast tblForecast);

    /**
     * 新增预测
     *
     * @param tblForecast 预测
     * @return 结果
     */
    public int insertTblForecast(TblForecast tblForecast);

    /**
     * 修改预测
     *
     * @param tblForecast 预测
     * @return 结果
     */
    public int updateTblForecast(TblForecast tblForecast);

    /**
     * 修改预测(不包含行项目)
     *
     * @param tblForecast 预测
     * @return 结果
     */
    public int updateTblForecastOnly(TblForecast tblForecast);

    /**
     * 批量删除预测
     *
     * @param forecastIds 需要删除的预测ID
     * @return 结果
     */
    public int deleteTblForecastByIds(Long[] forecastIds);

    /**
     * 删除预测信息
     *
     * @param forecastId 预测ID
     * @return 结果
     */
    public int deleteTblForecastById(Long forecastId);

    /**
     * 查询预测列表（包括预测行项目）--接口专用
     *
     * @param tblForecast 预测
     * @return 预测集合
     */
    public List<TblForecast> selectTblForecastFullList(TblForecast tblForecast);

    /**
     * 查询预测列表（包括预测行项目
     *
     * @param tblForecast 预测
     * @return 预测集合
     */
    public List<TblForecast> selectTblForecastWithItemList(TblForecast tblForecast);

    /**
     * 查询最大ID
     *
     * @return 最大ID
     */
    public Long selectLastId();

    /**
     * 查询预测行项目列表
     *
     * @param forecastItem 预测
     * @return 预测行项目集合
     */
    public List<TblForecastItem> selectTblForecastItemList(TblForecastItem forecastItem);

    /**
     * 查询指定预测编号的最大版本号
     *
     * @param forecastCode 预测编号
     * @return 最大版本号
     */
    public String selectMaxVersionByForecastCode(String forecastCode);

    /**
     * 查询预测(不抱行行项目)
     *
     * @param forecastId 预测ID
     * @return 预测
     */
    public TblForecast selectTblForecastOnlyById(Long forecastId);
}
