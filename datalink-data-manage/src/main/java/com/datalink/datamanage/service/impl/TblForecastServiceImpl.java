package com.datalink.datamanage.service.impl;

import com.datalink.common.utils.DateUtils;
import com.datalink.common.utils.StringUtils;
import com.datalink.datamanage.domain.TblForecast;
import com.datalink.datamanage.domain.TblForecastItem;
import com.datalink.datamanage.mapper.TblForecastMapper;
import com.datalink.datamanage.service.ITblForecastService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 预测Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-06-23
 */
@Service
public class TblForecastServiceImpl implements ITblForecastService
{
    @Autowired
    private TblForecastMapper tblForecastMapper;

    /**
     * 查询预测
     *
     * @param forecastId 预测ID
     * @return 预测
     */
    @Override
    public TblForecast selectTblForecastById(Long forecastId)
    {
        return tblForecastMapper.selectTblForecastById(forecastId);
    }

    /**
     * 查询预测列表
     *
     * @param tblForecast 预测
     * @return 预测
     */
    @Override
    public List<TblForecast> selectTblForecastList(TblForecast tblForecast)
    {
        return tblForecastMapper.selectTblForecastList(tblForecast);
    }

    /**
     * 新增预测
     *
     * @param tblForecast 预测
     * @return 结果
     */
    @Transactional
    @Override
    public int insertTblForecast(TblForecast tblForecast)
    {
        tblForecast.setCreateTime(DateUtils.getNowDate());
        int rows = tblForecastMapper.insertTblForecast(tblForecast);
        insertTblForecastItem(tblForecast);
        return rows;
    }

    /**
     * 修改预测
     *
     * @param tblForecast 预测
     * @return 结果
     */
    @Transactional
    @Override
    public int updateTblForecast(TblForecast tblForecast)
    {
        tblForecast.setUpdateTime(DateUtils.getNowDate());
        tblForecastMapper.deleteTblForecastItemByForecastId(tblForecast.getForecastId());
        insertTblForecastItem(tblForecast);
        return tblForecastMapper.updateTblForecast(tblForecast);
    }

    @Override
    public int updateTblForecastOnly(TblForecast tblForecast) {
        tblForecast.setUpdateTime(DateUtils.getNowDate());
        return tblForecastMapper.updateTblForecast(tblForecast);
    }

    /**
     * 批量删除预测
     *
     * @param forecastIds 需要删除的预测ID
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteTblForecastByIds(Long[] forecastIds)
    {
        tblForecastMapper.deleteTblForecastItemByForecastIds(forecastIds);
        return tblForecastMapper.deleteTblForecastByIds(forecastIds);
    }

    /**
     * 删除预测信息
     *
     * @param forecastId 预测ID
     * @return 结果
     */
    @Override
    public int deleteTblForecastById(Long forecastId)
    {
        tblForecastMapper.deleteTblForecastItemByForecastId(forecastId);
        return tblForecastMapper.deleteTblForecastById(forecastId);
    }

    /**
     * 新增预测行项目信息
     *
     * @param tblForecast 预测对象
     */
    public void insertTblForecastItem(TblForecast tblForecast)
    {
        List<TblForecastItem> tblForecastItemList = tblForecast.getDetail();
        Long forecastId = tblForecast.getForecastId();
        if (StringUtils.isNotNull(tblForecastItemList))
        {
            List<TblForecastItem> list = new ArrayList<TblForecastItem>();
            for (TblForecastItem tblForecastItem : tblForecastItemList)
            {
                tblForecastItem.setForecastId(forecastId);
                list.add(tblForecastItem);
            }
            if (list.size() > 0)
            {
                tblForecastMapper.batchTblForecastItem(list);
            }
        }
    }

    /**
     * 查询预测列表（包括预测行项目）--接口专用
     *
     * @param tblForecast 预测
     * @return 预测集合
     */
    @Override
    public List<TblForecast> selectTblForecastFullList(TblForecast tblForecast){
        return tblForecastMapper.selectTblForecastFullList(tblForecast);
    }

    @Override
    public List<TblForecast> selectTblForecastWithItemList(TblForecast tblForecast) {
        return tblForecastMapper.selectTblForecastWithItemList(tblForecast);
    }

    /**
     * 查询最大ID
     *
     * @return 最大ID
     */
    @Override
    public Long selectLastId(){
        return tblForecastMapper.selectLastId();
    }

    @Override
    public List<TblForecastItem> selectTblForecastItemList(TblForecastItem forecastItem) {
        return tblForecastMapper.selectTblForecastItemList(forecastItem);
    }

    @Override
    public String selectMaxVersionByForecastCode(String forecastCode) {
        return tblForecastMapper.selectMaxVersionByForecastCode(forecastCode);
    }

    @Override
    public List<TblForecast> selectTblForecastLatestVersionList(TblForecast tblForecast) {
        return tblForecastMapper.selectTblForecastLatestVersionList(tblForecast);
    }

    @Override
    public List<TblForecast> selectTblForecastLatestVersionWithItemList(TblForecast tblForecast) {
        return tblForecastMapper.selectTblForecastLatestVersionWithItemList(tblForecast);
    }

    /**
     * 查询预测(不抱行行项目)
     *
     * @param forecastId 预测ID
     * @return 预测
     */
    @Override
    public TblForecast selectTblForecastOnlyById(Long forecastId) {
        TblForecast param = new TblForecast();
        param.setForecastId(forecastId);
        return tblForecastMapper.selectTblForecastOnlyById(param);
    }
}
